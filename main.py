"""
Main Pipeline Script
Clean code implementation for complete analysis pipeline
"""

import logging
import sys
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from correlation_analyzer import CorrelationAnalyzer
from visualizer import Visualizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class AnalysisPipeline:
    """
    Main analysis pipeline orchestrator
    """
    
    def __init__(self):
        """Initialize pipeline components"""
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.correlation_analyzer = CorrelationAnalyzer()
        self.visualizer = Visualizer()

        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
    
    def run_complete_pipeline(self) -> None:
        """
        Execute complete analysis pipeline
        """
        logger.info("="*60)
        logger.info("STARTING COMPLETE ANALYSIS PIPELINE")
        logger.info("="*60)
        
        try:
            # Step 1: Data Processing
            logger.info("Step 1: Processing raw data...")
            processed_data = self.data_processor.process_all()
            logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")
            
            # Step 2: Correlation Analysis
            logger.info("Step 2: Running correlation analysis...")
            correlation_results = self.correlation_analyzer.run_comprehensive_analysis(processed_data)
            logger.info(f"✅ Correlation analysis completed. "
                       f"Found {len(correlation_results['significant_correlations'])} significant correlations")
            
            # Step 3: Visualization
            logger.info("Step 3: Creating visualizations...")
            self.visualizer.create_all_visualizations(
                processed_data, 
                correlation_results['significant_correlations']
            )
            logger.info("✅ Visualizations completed")
            
            # Step 4: Generate Summary Report
            logger.info("Step 4: Generating summary report...")
            self._generate_summary_report(processed_data, correlation_results)
            logger.info("✅ Summary report generated")
            
            logger.info("="*60)
            logger.info("PIPELINE COMPLETED SUCCESSFULLY!")
            logger.info("="*60)
            
            self._print_completion_summary(processed_data, correlation_results)
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise
    
    def _generate_summary_report(self, data: pd.DataFrame, 
                               results: dict) -> None:
        """Generate comprehensive summary report"""
        
        significant_corr = results['significant_correlations']
        mediation_results = results.get('mediation_results', [])
        moderation_results = results.get('moderation_results', [])
        
        report = f"""
# PHYSICAL ACTIVITY & PRODUCTIVITY ANALYSIS REPORT
## Generated by Clean Code Pipeline

### DATASET SUMMARY
- **Total observations**: {len(data)}
- **Unique participants**: {data['identity'].nunique() if 'identity' in data.columns else 'N/A'}
- **Variables analyzed**: {len(data.columns)}
- **Date range**: {data['year_week'].min() if 'year_week' in data.columns else 'N/A'} to {data['year_week'].max() if 'year_week' in data.columns else 'N/A'}

### CORRELATION ANALYSIS RESULTS
- **Total correlations tested**: {len(results['all_correlations'])}
- **Significant correlations**: {len(significant_corr)}
- **Success rate**: {len(significant_corr)/len(results['all_correlations'])*100:.1f}%

### TOP SIGNIFICANT FINDINGS
"""
        
        # Add top 5 significant correlations
        for i, (_, row) in enumerate(significant_corr.head(5).iterrows(), 1):
            report += f"""
{i}. **{row['predictor_variable']}** → **{row['target_variable']}**
   - Correlation: r = {row['correlation']:.3f}
   - P-value: {row['p_value']:.3f}
   - Strength: {row['strength']}
   - Direction: {row['direction']}
"""
        
        # Add mediation results if available
        if mediation_results:
            report += f"""
### MEDIATION ANALYSIS RESULTS
Found {len(mediation_results)} mediation pathways:
"""
            for i, mediation in enumerate(mediation_results, 1):
                report += f"""
{i}. **{mediation['x_variable']}** → **{mediation['mediator_variable']}** → **{mediation['y_variable']}**
   - Direct effect: {mediation['direct_effect']:.3f}
   - Indirect effect: {mediation['indirect_effect']:.3f}
   - Path A (X→M): {mediation['path_a']:.3f}
   - Path B (M→Y): {mediation['path_b']:.3f}
"""

        # Add moderation results if available
        if moderation_results:
            report += f"""
### MODERATION ANALYSIS RESULTS
Found {len(moderation_results)} moderation effects:
"""
            for i, moderation in enumerate(moderation_results, 1):
                if moderation.get('interaction_p_value', 1) < 0.05:  # Only significant interactions
                    report += f"""
{i}. **{moderation['x_variable']}** × **{moderation['moderator_variable']}** → **{moderation['y_variable']}**
   - Interaction effect: {moderation['interaction_effect']:.3f} (p = {moderation['interaction_p_value']:.3f})
   - High moderator slope: {moderation['simple_slope_high_moderator']:.3f}
   - Low moderator slope: {moderation['simple_slope_low_moderator']:.3f}
   - Moderator median: {moderation['moderator_median']:.2f}
"""
        
        report += f"""
### FILES GENERATED
- **Data**: `dataset/processed/weekly_merged_dataset_with_gamification.csv`
- **Correlations**: `results/reports/significant_correlations.csv`
- **Visualizations**: `results/visualizations/*.png`
- **This report**: `results/reports/analysis_summary.md`

### NEXT STEPS
1. Review significant correlations for practical implications
2. Examine visualizations for pattern insights
3. Consider intervention design based on strongest predictors
4. Validate findings with additional data if available

---
*Report generated by automated analysis pipeline*
"""
        
        # Save report
        report_path = Path("results/reports/analysis_summary.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"Summary report saved to {report_path}")
    
    def _print_completion_summary(self, data: pd.DataFrame, results: dict) -> None:
        """Print completion summary to console"""
        
        significant_corr = results['significant_correlations']
        
        print("\n" + "="*60)
        print("🎉 ANALYSIS PIPELINE COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        print(f"\n📊 DATASET PROCESSED:")
        print(f"   • {len(data)} weekly observations")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")
        print(f"   • {len(data.columns)} variables")
        
        print(f"\n🔍 CORRELATION ANALYSIS:")
        print(f"   • {len(results['all_correlations'])} total correlations tested")
        print(f"   • {len(significant_corr)} significant correlations found")
        print(f"   • {len(significant_corr)/len(results['all_correlations'])*100:.1f}% success rate")
        
        if not significant_corr.empty:
            strongest = significant_corr.iloc[0]
            print(f"\n🏆 STRONGEST FINDING:")
            print(f"   • {strongest['predictor_variable']} → {strongest['target_variable']}")
            print(f"   • r = {strongest['correlation']:.3f}, p = {strongest['p_value']:.3f}")
            print(f"   • {strongest['strength']} {strongest['direction']} correlation")
        
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/weekly_merged_dataset_with_gamification.csv")
        print(f"   • results/reports/significant_correlations.csv")
        print(f"   • results/reports/analysis_summary.md")
        print(f"   • results/visualizations/*.png")
        
        print(f"\n✅ All files ready for publication and further analysis!")
        print("="*60)


def main():
    """Main function"""
    try:
        # Check if raw data exists
        raw_data_path = Path("dataset/raw")
        if not raw_data_path.exists():
            print("❌ Raw data directory not found!")
            print("Please ensure dataset/raw/ contains strava.csv and pomokit.csv")
            return
        
        strava_file = raw_data_path / "strava.csv"
        pomokit_file = raw_data_path / "pomokit.csv"
        
        if not strava_file.exists() or not pomokit_file.exists():
            print("❌ Required data files not found!")
            print(f"Expected files:")
            print(f"  - {strava_file}")
            print(f"  - {pomokit_file}")
            return
        
        # Run pipeline
        pipeline = AnalysisPipeline()
        pipeline.run_complete_pipeline()
        
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
