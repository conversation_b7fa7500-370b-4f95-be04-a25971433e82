"""
Correlation Analysis Module
Clean code implementation for correlation and mediation analysis
"""

import pandas as pd
from scipy.stats import pearsonr
from typing import Dict, List, Any
import logging
from pathlib import Path

from constants import AnalysisConstants

logger = logging.getLogger(__name__)

class CorrelationAnalyzer:
    """
    Clean correlation analysis class following SOLID principles
    """
    
    def __init__(self, significance_level: float = AnalysisConstants.DEFAULT_SIGNIFICANCE_LEVEL):
        """
        Initialize correlation analyzer

        Args:
            significance_level: Statistical significance threshold
        """
        self.significance_level = significance_level
        self.results_path = Path(AnalysisConstants.RESULTS_PATH)
        self.results_path.mkdir(parents=True, exist_ok=True)
        
    def analyze_correlations(self, data: pd.DataFrame, 
                           target_variable: str,
                           predictor_variables: List[str]) -> pd.DataFrame:
        """
        Analyze correlations between predictors and target variable
        
        Args:
            data: Input dataset
            target_variable: Dependent variable name
            predictor_variables: List of independent variable names
            
        Returns:
            DataFrame with correlation results
        """
        logger.info(f"Analyzing correlations for target: {target_variable}")
        
        results = []
        
        for predictor in predictor_variables:
            if predictor in data.columns and target_variable in data.columns:
                result = self._calculate_correlation(data, predictor, target_variable)
                if result:
                    results.append(result)
        
        results_df = pd.DataFrame(results)
        
        # Sort by absolute correlation strength
        if not results_df.empty:
            results_df = results_df.reindex(
                results_df['correlation'].abs().sort_values(ascending=False).index
            )
        
        logger.info(f"Found {len(results_df)} correlations, "
                   f"{len(results_df[results_df['significant']])} significant")
        
        return results_df
    
    def _calculate_correlation(self, data: pd.DataFrame, 
                             predictor: str, target: str) -> Dict[str, Any]:
        """
        Calculate correlation between two variables
        
        Args:
            data: Input dataset
            predictor: Predictor variable name
            target: Target variable name
            
        Returns:
            Dictionary with correlation results
        """
        # Clean data (remove missing values)
        clean_data = data[[predictor, target]].dropna()
        
        if len(clean_data) < AnalysisConstants.MINIMUM_SAMPLE_SIZE:
            logger.warning(f"Insufficient data for {predictor} vs {target}")
            return None
        
        # Calculate correlation
        r, p = pearsonr(clean_data[predictor], clean_data[target])
        
        # Interpret strength
        strength = self._interpret_correlation_strength(abs(r))
        direction = "positive" if r > 0 else "negative"
        significant = p < self.significance_level
        
        return {
            'predictor_variable': predictor,
            'target_variable': target,
            'correlation': r,
            'p_value': p,
            'sample_size': len(clean_data),
            'strength': strength,
            'direction': direction,
            'significant': significant
        }
    
    def _interpret_correlation_strength(self, abs_r: float) -> str:
        """Interpret correlation strength based on absolute value"""
        if abs_r >= AnalysisConstants.VERY_LARGE_THRESHOLD:
            return "very_large"
        elif abs_r >= AnalysisConstants.LARGE_THRESHOLD:
            return "large"
        elif abs_r >= AnalysisConstants.MEDIUM_THRESHOLD:
            return "medium"
        elif abs_r >= AnalysisConstants.SMALL_THRESHOLD:
            return "small"
        else:
            return "negligible"
    
    def analyze_mediation(self, data: pd.DataFrame,
                         x_var: str, m_var: str, y_var: str) -> Dict[str, float]:
        """
        Analyze mediation pathway X → M → Y
        
        Args:
            data: Input dataset
            x_var: Predictor variable (X)
            m_var: Mediator variable (M)
            y_var: Outcome variable (Y)
            
        Returns:
            Dictionary with mediation results
        """
        logger.info(f"Analyzing mediation: {x_var} → {m_var} → {y_var}")
        
        # Clean data
        clean_data = data[[x_var, m_var, y_var]].dropna()
        
        if len(clean_data) < AnalysisConstants.MINIMUM_SAMPLE_SIZE:
            logger.warning("Insufficient data for mediation analysis")
            return {}
        
        # Calculate paths
        r_direct, p_direct = pearsonr(clean_data[x_var], clean_data[y_var])  # X → Y
        r_a, p_a = pearsonr(clean_data[x_var], clean_data[m_var])            # X → M
        r_b, p_b = pearsonr(clean_data[m_var], clean_data[y_var])            # M → Y
        
        # Calculate indirect effect
        indirect_effect = r_a * r_b
        
        return {
            'x_variable': x_var,
            'mediator_variable': m_var,
            'y_variable': y_var,
            'direct_effect': r_direct,
            'direct_p_value': p_direct,
            'path_a': r_a,
            'path_a_p_value': p_a,
            'path_b': r_b,
            'path_b_p_value': p_b,
            'indirect_effect': indirect_effect,
            'sample_size': len(clean_data)
        }
    
    def get_variable_groups(self) -> Dict[str, List[str]]:
        """
        Define variable groups for analysis
        
        Returns:
            Dictionary of variable groups
        """
        return {
            'physical_activity': [
                'total_distance_km',
                'avg_distance_km', 
                'activity_days',
                'total_time_minutes',
                'avg_time_minutes',
                'avg_intensity',
                'consistency_score'
            ],
            'gamification': [
                'activity_points',
                'productivity_points',
                'total_gamification_points',
                'achievement_rate',
                'gamification_balance'
            ],
            'productivity': [
                'total_cycles',
                'work_days',
                'total_screenshots',
                'avg_productivity_score',
                'weekly_efficiency'
            ]
        }
    
    def run_comprehensive_analysis(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        Run comprehensive correlation and mediation analysis
        
        Args:
            data: Input dataset
            
        Returns:
            Dictionary with all analysis results
        """
        logger.info("Running comprehensive correlation analysis...")
        
        variable_groups = self.get_variable_groups()
        target_variable = 'total_cycles'  # Primary outcome
        
        # Combine all predictor variables
        all_predictors = (variable_groups['physical_activity'] + 
                         variable_groups['gamification'])
        
        # Analyze correlations
        correlation_results = self.analyze_correlations(
            data, target_variable, all_predictors
        )
        
        # Filter significant correlations
        significant_results = correlation_results[
            correlation_results['significant']
        ].copy()
        
        # Analyze key mediation pathways
        mediation_results = self._analyze_key_mediations(data)

        # Analyze key moderation effects
        moderation_results = self._analyze_key_moderations(data)

        # Save results
        self._save_results(correlation_results, significant_results, mediation_results, moderation_results)

        return {
            'all_correlations': correlation_results,
            'significant_correlations': significant_results,
            'mediation_results': mediation_results,
            'moderation_results': moderation_results
        }
    
    def _analyze_key_mediations(self, data: pd.DataFrame) -> List[Dict[str, float]]:
        """Analyze key mediation pathways"""

        mediation_pathways = [
            ('activity_days', 'activity_points', 'total_cycles'),
            ('activity_days', 'total_gamification_points', 'total_cycles'),
            ('consistency_score', 'achievement_rate', 'total_cycles')
        ]

        mediation_results = []

        for x_var, m_var, y_var in mediation_pathways:
            if all(var in data.columns for var in [x_var, m_var, y_var]):
                result = self.analyze_mediation(data, x_var, m_var, y_var)
                if result:
                    mediation_results.append(result)

        return mediation_results

    def _analyze_key_moderations(self, data: pd.DataFrame) -> List[Dict[str, float]]:
        """Analyze key moderation pathways"""

        moderation_pathways = [
            # Gamification balance as moderator (key finding!)
            ('activity_days', 'gamification_balance', 'total_cycles'),
            ('consistency_score', 'gamification_balance', 'total_cycles'),
            ('total_distance_km', 'gamification_balance', 'total_cycles'),

            # Activity frequency as moderator
            ('consistency_score', 'activity_days', 'total_cycles'),
            ('total_time_minutes', 'activity_days', 'total_cycles'),

            # Achievement rate as moderator
            ('activity_points', 'achievement_rate', 'total_cycles'),
            ('total_gamification_points', 'achievement_rate', 'total_cycles')
        ]

        moderation_results = []

        for x_var, mod_var, y_var in moderation_pathways:
            if all(var in data.columns for var in [x_var, mod_var, y_var]):
                result = self.analyze_moderation(data, x_var, mod_var, y_var)
                if result:
                    moderation_results.append(result)

        return moderation_results

    def analyze_moderation(self, data: pd.DataFrame,
                          x_var: str, moderator_var: str, y_var: str) -> Dict[str, float]:
        """
        Analyze moderation effect: Y = β₀ + β₁X + β₂M + β₃(X×M) + ε

        Args:
            data: Input dataset
            x_var: Predictor variable (X)
            moderator_var: Moderator variable (M)
            y_var: Outcome variable (Y)

        Returns:
            Dictionary with moderation results
        """
        logger.info(f"Analyzing moderation: {x_var} × {moderator_var} → {y_var}")

        # Clean data
        clean_data = data[[x_var, moderator_var, y_var]].dropna()

        if len(clean_data) < AnalysisConstants.MINIMUM_SAMPLE_SIZE:
            logger.warning("Insufficient data for moderation analysis")
            return {}

        # Standardize variables for interaction
        from scipy.stats import zscore
        clean_data = clean_data.copy()
        clean_data[f'{x_var}_z'] = zscore(clean_data[x_var])
        clean_data[f'{moderator_var}_z'] = zscore(clean_data[moderator_var])
        clean_data['interaction'] = clean_data[f'{x_var}_z'] * clean_data[f'{moderator_var}_z']

        # Calculate correlations for simple effects
        r_x_y, p_x_y = pearsonr(clean_data[x_var], clean_data[y_var])
        r_m_y, p_m_y = pearsonr(clean_data[moderator_var], clean_data[y_var])
        r_int_y, p_int_y = pearsonr(clean_data['interaction'], clean_data[y_var])

        # Simple slopes analysis (high/low moderator)
        moderator_median = clean_data[moderator_var].median()
        high_mod = clean_data[clean_data[moderator_var] >= moderator_median]
        low_mod = clean_data[clean_data[moderator_var] < moderator_median]

        r_high, p_high = pearsonr(high_mod[x_var], high_mod[y_var]) if len(high_mod) > 10 else (0, 1)
        r_low, p_low = pearsonr(low_mod[x_var], low_mod[y_var]) if len(low_mod) > 10 else (0, 1)

        return {
            'x_variable': x_var,
            'moderator_variable': moderator_var,
            'y_variable': y_var,
            'main_effect_x': r_x_y,
            'main_effect_x_p': p_x_y,
            'main_effect_moderator': r_m_y,
            'main_effect_moderator_p': p_m_y,
            'interaction_effect': r_int_y,
            'interaction_p_value': p_int_y,
            'simple_slope_high_moderator': r_high,
            'simple_slope_high_p': p_high,
            'simple_slope_low_moderator': r_low,
            'simple_slope_low_p': p_low,
            'moderator_median': moderator_median,
            'sample_size': len(clean_data),
            'high_moderator_n': len(high_mod),
            'low_moderator_n': len(low_mod)
        }
    
    def _save_results(self, all_correlations: pd.DataFrame,
                     significant_correlations: pd.DataFrame,
                     mediation_results: List[Dict],
                     moderation_results: List[Dict] = None) -> None:
        """Save analysis results to files"""

        # Save correlation results
        all_correlations.to_csv(
            self.results_path / AnalysisConstants.ALL_CORRELATIONS_FILE, index=False
        )
        significant_correlations.to_csv(
            self.results_path / AnalysisConstants.SIGNIFICANT_CORRELATIONS_FILE, index=False
        )

        # Save mediation results
        if mediation_results:
            mediation_df = pd.DataFrame(mediation_results)
            mediation_df.to_csv(
                self.results_path / AnalysisConstants.MEDIATION_RESULTS_FILE, index=False
            )

        # Save moderation results
        if moderation_results:
            moderation_df = pd.DataFrame(moderation_results)
            moderation_df.to_csv(
                self.results_path / AnalysisConstants.MODERATION_RESULTS_FILE, index=False
            )

        logger.info("Analysis results saved to results/reports/")
    
    def generate_summary_report(self, results: Dict[str, pd.DataFrame]) -> str:
        """
        Generate summary report of analysis results
        
        Args:
            results: Analysis results dictionary
            
        Returns:
            Summary report as string
        """
        significant_corr = results['significant_correlations']
        
        if significant_corr.empty:
            return "No significant correlations found."
        
        # Find strongest correlations
        strongest_positive = significant_corr[
            significant_corr['direction'] == 'positive'
        ].iloc[0] if any(significant_corr['direction'] == 'positive') else None
        
        strongest_negative = significant_corr[
            significant_corr['direction'] == 'negative'
        ].iloc[0] if any(significant_corr['direction'] == 'negative') else None
        
        report = f"""
CORRELATION ANALYSIS SUMMARY
============================

Total correlations tested: {len(results['all_correlations'])}
Significant correlations: {len(significant_corr)}
Success rate: {len(significant_corr)/len(results['all_correlations'])*100:.1f}%

STRONGEST FINDINGS:
"""
        
        if strongest_positive is not None:
            report += f"""
Strongest Positive: {strongest_positive['predictor_variable']} → {strongest_positive['target_variable']}
Correlation: r = {strongest_positive['correlation']:.3f}, p = {strongest_positive['p_value']:.3f}
Strength: {strongest_positive['strength']}
"""
        
        if strongest_negative is not None:
            report += f"""
Strongest Negative: {strongest_negative['predictor_variable']} → {strongest_negative['target_variable']}
Correlation: r = {strongest_negative['correlation']:.3f}, p = {strongest_negative['p_value']:.3f}
Strength: {strongest_negative['strength']}
"""
        
        return report


def main():
    """Main function for testing correlation analyzer"""
    # Load processed data
    data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
    
    if not data_path.exists():
        print("Processed data not found. Run data_processor.py first.")
        return
    
    data = pd.read_csv(data_path)
    
    # Run analysis
    analyzer = CorrelationAnalyzer()
    results = analyzer.run_comprehensive_analysis(data)
    
    # Generate summary
    summary = analyzer.generate_summary_report(results)
    print(summary)


if __name__ == "__main__":
    main()
