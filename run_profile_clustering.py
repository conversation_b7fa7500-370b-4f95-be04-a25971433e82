"""
Run Profile-Based Clustering Analysis
Uses the 3 main user profiles as foundation for clustering
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
import sys
import matplotlib.pyplot as plt
import seaborn as sns

# Add src to path
sys.path.append('src')

from profile_based_clustering import ProfileBasedClusteringAnalyzer

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main profile-based clustering pipeline"""
    
    logger.info("="*60)
    logger.info("STARTING PROFILE-BASED CLUSTERING ANALYSIS")
    logger.info("="*60)
    
    try:
        # Setup paths
        data_path = Path("dataset/processed")
        results_path = Path("results")
        visualizations_path = results_path / "visualizations"
        reports_path = results_path / "reports"
        
        # Ensure directories exist
        visualizations_path.mkdir(parents=True, exist_ok=True)
        reports_path.mkdir(parents=True, exist_ok=True)
        
        # Step 1: Load processed data
        logger.info("Step 1: Loading processed data...")
        data_file = data_path / "weekly_merged_dataset_with_gamification.csv"
        
        if not data_file.exists():
            logger.error(f"Data file not found: {data_file}")
            logger.info("Please run main.py first to generate processed data")
            return
        
        data = pd.read_csv(data_file)
        logger.info(f"✅ Data loaded: {len(data)} observations")
        
        # Step 2: Initialize profile-based clustering analyzer
        logger.info("Step 2: Initializing profile-based clustering analyzer...")
        analyzer = ProfileBasedClusteringAnalyzer(visualizations_path)
        
        # Step 3: Prepare data with 3 main profiles
        logger.info("Step 3: Preparing data with 3 main profiles...")
        user_data = analyzer.prepare_profile_data(data)
        logger.info(f"✅ Profile data prepared: {len(user_data)} unique users")
        
        # Step 4: Perform profile-based clustering analysis
        logger.info("Step 4: Performing profile-based clustering analysis...")
        clustering_results = analyzer.perform_profile_clustering(user_data)
        
        # Display key results
        logger.info("\n" + "="*50)
        logger.info("📊 PROFILE-BASED CLUSTERING RESULTS")
        logger.info("="*50)
        
        # K=3 Analysis
        k3_results = clustering_results['k3_analysis']
        logger.info(f"\n🎯 K=3 CLUSTERING (Profile-Aligned):")
        logger.info(f"  Silhouette Score: {k3_results['silhouette_score']:.3f}")
        logger.info(f"  ARI with Profiles: {k3_results['ari_with_profiles']:.3f}")
        logger.info(f"  Calinski-Harabasz: {k3_results['calinski_score']:.1f}")
        
        # Profile Validation
        validation = clustering_results['profile_validation']
        logger.info(f"\n✅ PROFILE VALIDATION:")
        logger.info(f"  Best K for Profiles: {validation['best_k_for_profiles']}")
        logger.info(f"  Best ARI Score: {validation['best_ari_score']:.3f}")
        logger.info(f"  Interpretation: {validation['interpretation']}")
        
        # Optimal Analysis
        optimal = clustering_results['optimal_analysis']
        recommendation = optimal['recommendation']
        logger.info(f"\n🎯 OPTIMAL CLUSTER RECOMMENDATION:")
        logger.info(f"  Final Recommendation: K={recommendation['final_recommendation']}")
        logger.info(f"  Reason: {recommendation['reason']}")
        logger.info(f"  Confidence: {recommendation['confidence']}")
        logger.info(f"  K=3 Silhouette: {recommendation['k3_silhouette']:.3f}")
        logger.info(f"  Best Silhouette K: {recommendation['statistical_optimal']} (score: {optimal['max_silhouette_score']:.3f})")
        logger.info(f"  Elbow Point: K={recommendation['elbow_point']}")
        
        # Profile Characteristics
        characteristics = clustering_results['profile_characteristics']
        logger.info(f"\n👥 PROFILE CHARACTERISTICS:")
        for profile, stats in characteristics.items():
            logger.info(f"  {profile}:")
            logger.info(f"    Count: {stats['count']} users ({stats['percentage']:.1f}%)")
            logger.info(f"    Avg Consistency: {stats['avg_consistency']:.2f}")
            logger.info(f"    Avg Achievement: {stats['avg_achievement']:.2f}")
            logger.info(f"    Avg Balance: {stats['avg_balance']:.1f}")
            logger.info(f"    Avg Cycles: {stats['avg_cycles']:.1f}")
        
        # Cluster-Profile Mapping
        enhancement = clustering_results['profile_enhancement']
        mapping = enhancement['cluster_profile_mapping']
        logger.info(f"\n🔗 CLUSTER-PROFILE MAPPING:")
        for cluster_id, info in mapping.items():
            logger.info(f"  Cluster {cluster_id}:")
            logger.info(f"    Dominant Profile: {info['dominant_profile']}")
            logger.info(f"    Purity: {info['purity']:.2f}")
            logger.info(f"    Avg Cycles: {info['avg_metrics']['cycles']:.1f}")
        
        # Step 5: Generate comprehensive report
        logger.info("\nStep 5: Generating profile-based clustering report...")
        report = generate_profile_clustering_report(user_data, clustering_results)
        
        # Save report
        report_file = reports_path / "profile_based_clustering_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"✅ Profile clustering report saved: {report_file}")
        
        # Step 6: Save enhanced user data
        logger.info("Step 6: Saving enhanced user data...")
        enhanced_data = enhancement['enhanced_user_data']
        
        # Save to CSV
        enhanced_file = reports_path / "profile_based_clustering_results.csv"
        enhanced_data.to_csv(enhanced_file, index=False)
        logger.info(f"✅ Enhanced data saved: {enhanced_file}")
        
        # Step 7: Create visualizations
        logger.info("Step 7: Creating profile-based clustering visualizations...")
        create_profile_clustering_visualizations(
            user_data, clustering_results, visualizations_path
        )
        logger.info("✅ Visualizations completed")
        
        # Final summary
        logger.info("\n" + "="*60)
        logger.info("🎉 PROFILE-BASED CLUSTERING COMPLETED!")
        logger.info("="*60)
        
        logger.info(f"\n📁 FILES GENERATED:")
        logger.info(f"   📋 Report: profile_based_clustering_report.md")
        logger.info(f"   📈 Data: profile_based_clustering_results.csv")
        logger.info(f"   📊 Visualizations: Profile clustering charts")
        
        logger.info(f"\n🎯 KEY FINDINGS:")
        logger.info(f"   • Recommended K: {recommendation['final_recommendation']}")
        logger.info(f"   • Profile validation: {validation['interpretation']}")
        logger.info(f"   • K=3 performance: {k3_results['silhouette_score']:.3f} silhouette")
        logger.info(f"   • Best statistical K: {recommendation['statistical_optimal']}")
        
        logger.info(f"\n✅ Clean, interpretable clustering based on existing profiles!")
        
    except Exception as e:
        logger.error(f"❌ Error in profile-based clustering: {str(e)}")
        raise

def generate_profile_clustering_report(user_data: pd.DataFrame, 
                                     clustering_results: dict) -> str:
    """Generate comprehensive profile-based clustering report"""
    
    k3_results = clustering_results['k3_analysis']
    validation = clustering_results['profile_validation']
    optimal = clustering_results['optimal_analysis']
    characteristics = clustering_results['profile_characteristics']
    enhancement = clustering_results['profile_enhancement']
    recommendation = optimal['recommendation']
    
    report = f"""# PROFILE-BASED CLUSTERING ANALYSIS
## Clustering Analysis Menggunakan 3 Profil Utama sebagai Foundation

---

## 🎯 EXECUTIVE SUMMARY

Analisis clustering dilakukan dengan menggunakan **3 profil utama** sebagai foundation, memberikan hasil yang **clean, konsisten, dan mudah diinterpretasi**. Menggunakan {len(user_data)} pengguna dengan fokus pada alignment antara clustering dan existing profiles.

**Key Findings:**
- **Recommended K**: {recommendation['final_recommendation']} clusters
- **Profile Validation**: {validation['interpretation']}
- **K=3 Performance**: {k3_results['silhouette_score']:.3f} silhouette score
- **Confidence Level**: {recommendation['confidence']}

---

## 👥 DISTRIBUSI 3 PROFIL UTAMA

"""
    
    # Add profile distribution
    for profile, stats in characteristics.items():
        report += f"### {profile}\n"
        report += f"- **Count**: {stats['count']} users ({stats['percentage']:.1f}%)\n"
        report += f"- **Avg Consistency**: {stats['avg_consistency']:.2f}\n"
        report += f"- **Avg Achievement**: {stats['avg_achievement']:.2f}\n"
        report += f"- **Avg Balance**: {stats['avg_balance']:.1f}\n"
        report += f"- **Avg Cycles**: {stats['avg_cycles']:.1f}\n\n"
    
    report += f"""---

## 📊 CLUSTERING ANALYSIS RESULTS

### K=3 Clustering (Profile-Aligned)
- **Silhouette Score**: {k3_results['silhouette_score']:.3f}
- **ARI with Profiles**: {k3_results['ari_with_profiles']:.3f}
- **Calinski-Harabasz**: {k3_results['calinski_score']:.1f}

### Profile Validation Results
- **Best K for Profile Alignment**: {validation['best_k_for_profiles']}
- **Best ARI Score**: {validation['best_ari_score']:.3f}
- **Interpretation**: {validation['interpretation']}

### Optimal Cluster Analysis
- **Statistical Optimal**: K={recommendation['statistical_optimal']} (silhouette: {optimal['max_silhouette_score']:.3f})
- **Elbow Point**: K={recommendation['elbow_point']}
- **Profile-Aligned**: K=3 (silhouette: {recommendation['k3_silhouette']:.3f})

---

## 🎯 FINAL RECOMMENDATION

### Recommended Approach: K={recommendation['final_recommendation']}

**Reasoning**: {recommendation['reason']}

**Confidence Level**: {recommendation['confidence']}

### Why This Approach Works:
1. **Clean Interpretation**: Aligns dengan existing profile framework
2. **Statistical Soundness**: Balance antara performance dan interpretability
3. **Business Alignment**: Mudah dijelaskan ke stakeholders
4. **Implementation Ready**: Consistent dengan existing systems

---

## 🔗 CLUSTER-PROFILE MAPPING

"""
    
    # Add cluster mapping
    mapping = enhancement['cluster_profile_mapping']
    for cluster_id, info in mapping.items():
        report += f"### Cluster {cluster_id}\n"
        report += f"- **Dominant Profile**: {info['dominant_profile']}\n"
        report += f"- **Purity**: {info['purity']:.2f} ({info['purity']*100:.1f}%)\n"
        report += f"- **Average Metrics**:\n"
        report += f"  - Consistency: {info['avg_metrics']['consistency']:.2f}\n"
        report += f"  - Achievement: {info['avg_metrics']['achievement']:.2f}\n"
        report += f"  - Balance: {info['avg_metrics']['balance']:.1f}\n"
        report += f"  - Cycles: {info['avg_metrics']['cycles']:.1f}\n\n"
    
    report += f"""---

## ✅ IMPLEMENTATION RECOMMENDATIONS

### 1. Use K={recommendation['final_recommendation']} for Production
- **Algorithm**: KMeans with {recommendation['final_recommendation']} clusters
- **Features**: consistency_score, achievement_rate, gamification_balance, total_cycles, activity_days, total_distance_km
- **Validation**: Regular monitoring dengan profile alignment metrics

### 2. Profile-Cluster Integration
- **Maintain** existing 3 profile framework
- **Enhance** dengan clustering insights
- **Validate** user assignments dengan cluster membership

### 3. Personalization Strategy
- **Primary**: Use existing profiles untuk main personalization
- **Secondary**: Use cluster assignments untuk fine-tuning
- **Monitoring**: Track profile-cluster alignment over time

### 4. Success Metrics
- **Profile Alignment**: ARI score > 0.3
- **Cluster Quality**: Silhouette score > 0.25
- **Business Impact**: User engagement improvement
- **Stability**: Consistent cluster assignments over time

---

## 🔧 TECHNICAL IMPLEMENTATION

### Database Schema
```sql
ALTER TABLE users ADD COLUMN cluster_id INTEGER;
ALTER TABLE users ADD COLUMN cluster_dominant_profile VARCHAR(50);
ALTER TABLE users ADD COLUMN profile_cluster_aligned BOOLEAN;
ALTER TABLE users ADD COLUMN cluster_purity FLOAT;
```

### API Integration
```python
# Get user cluster assignment
def get_user_cluster(user_id):
    return {{
        'main_profile': user.main_profile,
        'cluster_id': user.cluster_id,
        'cluster_dominant_profile': user.cluster_dominant_profile,
        'profile_cluster_aligned': user.profile_cluster_aligned
    }}
```

---

## 📈 ADVANTAGES OF PROFILE-BASED APPROACH

### 1. **Clean & Interpretable**
- Aligns dengan existing business logic
- Easy to explain to stakeholders
- Consistent dengan current systems

### 2. **Statistically Sound**
- Validated dengan multiple clustering metrics
- Balance antara performance dan interpretability
- Robust methodology

### 3. **Implementation Ready**
- No major system changes required
- Gradual enhancement possible
- Clear migration path

### 4. **Business Aligned**
- Supports existing personalization strategies
- Enhances current user segmentation
- Provides validation untuk profile framework

---

## 🎯 NEXT STEPS

### Immediate (Week 1-2)
1. **Validate** clustering results dengan business stakeholders
2. **Implement** K={recommendation['final_recommendation']} clustering dalam development
3. **Test** profile-cluster alignment dengan sample users

### Short-term (Month 1)
1. **Deploy** clustering enhancement ke production
2. **Monitor** cluster stability dan profile alignment
3. **Measure** business impact pada user engagement

### Long-term (Quarter 1)
1. **Optimize** clustering parameters berdasarkan performance data
2. **Expand** clustering insights untuk advanced personalization
3. **Iterate** pada profile framework berdasarkan clustering learnings

---

## ✅ CONCLUSION

Profile-based clustering approach memberikan **best of both worlds**:
- **Maintains** existing profile framework yang sudah proven
- **Enhances** dengan clustering insights yang statistically sound
- **Provides** clear path untuk implementation dan optimization

**Result**: Clean, interpretable, dan actionable clustering solution yang ready untuk production implementation.

---

*Report generated from profile-based clustering analysis of {len(user_data)} users with focus on 3 main profiles and statistical validation.*
"""
    
    return report

def create_profile_clustering_visualizations(user_data: pd.DataFrame, 
                                           clustering_results: dict,
                                           output_path: Path):
    """Create visualizations for profile-based clustering"""
    
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. Profile distribution
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Profile counts
    profile_counts = user_data['main_profile'].value_counts()
    ax1.pie(profile_counts.values, labels=profile_counts.index, autopct='%1.1f%%')
    ax1.set_title('Distribution of 3 Main Profiles')
    
    # Profile characteristics scatter
    for profile in user_data['main_profile'].unique():
        profile_data = user_data[user_data['main_profile'] == profile]
        ax2.scatter(profile_data['consistency_score'], 
                   profile_data['achievement_rate'],
                   label=profile, alpha=0.7)
    ax2.set_xlabel('Consistency Score')
    ax2.set_ylabel('Achievement Rate')
    ax2.set_title('Profile Characteristics')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Clustering validation
    validation = clustering_results['profile_validation']['validation_results']
    k_values = [int(k.split('_')[1]) for k in validation.keys()]
    ari_scores = [validation[k]['ari_with_profiles'] for k in validation.keys()]
    silhouette_scores = [validation[k]['silhouette_score'] for k in validation.keys()]
    
    ax3.plot(k_values, ari_scores, 'bo-', label='ARI with Profiles')
    ax3.set_xlabel('Number of Clusters (K)')
    ax3.set_ylabel('Adjusted Rand Index')
    ax3.set_title('Profile Validation: ARI Scores')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    ax4.plot(k_values, silhouette_scores, 'ro-', label='Silhouette Score')
    ax4.set_xlabel('Number of Clusters (K)')
    ax4.set_ylabel('Silhouette Score')
    ax4.set_title('Clustering Quality: Silhouette Scores')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig(output_path / '14_profile_based_clustering.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info("Saved: 14_profile_based_clustering.png")

if __name__ == "__main__":
    main()
