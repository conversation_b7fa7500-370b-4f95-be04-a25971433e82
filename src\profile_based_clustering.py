"""
Profile-Based Clustering Analysis
Uses the 3 main user profiles as the foundation for clustering
"""

import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score, adjusted_rand_score, calinski_harabasz_score
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ProfileBasedClusteringAnalyzer:
    """
    Clustering analysis based on the 3 main user profiles
    """
    
    def __init__(self, output_path: Path):
        self.output_path = output_path
        self.scaler = StandardScaler()
        
    def classify_user_profile(self, consistency, achievement, balance):
        """Classify users into the 3 main profiles"""
        if consistency > 0.8 and achievement > 0.7 and balance < 40:
            return "Optimal Performer"
        elif achievement > 0.7 and balance < 60:
            return "Balanced Achiever"  # High achievement, manageable balance
        elif achievement > 0.7 and balance >= 60:
            return "Imbalanced Moderate"  # High achievement, poor balance
        else:
            return "Struggling Beginner"
    
    def prepare_profile_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare data with 3 main profiles
        """
        logger.info("Preparing data with 3 main user profiles...")
        
        # Aggregate to user level
        if 'identity' in data.columns:
            user_data = data.groupby('identity').agg({
                'consistency_score': 'mean',
                'achievement_rate': 'mean', 
                'gamification_balance': 'mean',
                'total_cycles': 'mean',
                'activity_days': 'mean',
                'total_distance_km': 'mean',
                'total_time_minutes': 'mean',
                'activity_points': 'mean',
                'productivity_points': 'mean',
                'total_gamification_points': 'mean'
            }).reset_index()
        else:
            user_data = data.copy()
            
        # Classify into 3 main profiles
        user_data['main_profile'] = user_data.apply(
            lambda row: self.classify_user_profile(
                row['consistency_score'],
                row['achievement_rate'], 
                row['gamification_balance']
            ), axis=1
        )
        
        logger.info(f"Prepared profile data: {len(user_data)} users")
        
        # Show distribution
        profile_counts = user_data['main_profile'].value_counts()
        logger.info("Profile distribution:")
        for profile, count in profile_counts.items():
            percentage = (count / len(user_data)) * 100
            logger.info(f"  {profile}: {count} users ({percentage:.1f}%)")
            
        return user_data
    
    def perform_profile_clustering(self, user_data: pd.DataFrame) -> dict:
        """
        Perform clustering analysis using 3 profiles as foundation
        """
        logger.info("Starting profile-based clustering analysis...")
        
        # Select features for clustering
        clustering_features = [
            'consistency_score', 'achievement_rate', 'gamification_balance',
            'total_cycles', 'activity_days', 'total_distance_km'
        ]
        
        X = user_data[clustering_features].fillna(0)
        X_scaled = self.scaler.fit_transform(X)
        
        results = {}
        
        # 1. Test K=3 clustering (matching 3 profiles)
        results['k3_analysis'] = self._analyze_k3_clustering(X_scaled, user_data)
        
        # 2. Validate profiles with clustering
        results['profile_validation'] = self._validate_profiles_clustering(X_scaled, user_data)
        
        # 3. Optimal clusters for this specific data
        results['optimal_analysis'] = self._find_optimal_for_profiles(X_scaled)
        
        # 4. Profile characteristics analysis
        results['profile_characteristics'] = self._analyze_profile_characteristics(user_data)
        
        # 5. Clustering-based profile enhancement
        results['profile_enhancement'] = self._enhance_profiles_with_clustering(
            X_scaled, user_data, results['k3_analysis']
        )
        
        return results
    
    def _analyze_k3_clustering(self, X_scaled: np.ndarray, user_data: pd.DataFrame) -> dict:
        """Analyze K=3 clustering to match 3 main profiles"""
        logger.info("Analyzing K=3 clustering...")
        
        kmeans_3 = KMeans(n_clusters=3, random_state=42, n_init=10)
        cluster_labels = kmeans_3.fit_predict(X_scaled)
        
        # Calculate metrics
        silhouette_avg = silhouette_score(X_scaled, cluster_labels)
        calinski_score = calinski_harabasz_score(X_scaled, cluster_labels)
        
        # Compare with existing profiles
        profile_labels = user_data['main_profile'].factorize()[0]
        ari_score = adjusted_rand_score(profile_labels, cluster_labels)
        
        # Analyze cluster composition
        cluster_composition = self._analyze_cluster_composition(
            user_data, cluster_labels
        )
        
        return {
            'cluster_labels': cluster_labels,
            'silhouette_score': silhouette_avg,
            'calinski_score': calinski_score,
            'ari_with_profiles': ari_score,
            'cluster_composition': cluster_composition,
            'centroids': kmeans_3.cluster_centers_
        }
    
    def _validate_profiles_clustering(self, X_scaled: np.ndarray, user_data: pd.DataFrame) -> dict:
        """Validate existing profiles using clustering"""
        logger.info("Validating existing profiles with clustering...")
        
        # Test different K values
        k_values = [2, 3, 4, 5]
        validation_results = {}
        
        for k in k_values:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(X_scaled)
            
            silhouette_avg = silhouette_score(X_scaled, cluster_labels)
            
            # Compare with profiles
            profile_labels = user_data['main_profile'].factorize()[0]
            ari_score = adjusted_rand_score(profile_labels, cluster_labels)
            
            validation_results[f'k_{k}'] = {
                'silhouette_score': silhouette_avg,
                'ari_with_profiles': ari_score,
                'cluster_labels': cluster_labels
            }
        
        # Find best K for profile validation
        best_k = max(validation_results.keys(), 
                    key=lambda k: validation_results[k]['ari_with_profiles'])
        
        return {
            'validation_results': validation_results,
            'best_k_for_profiles': best_k,
            'best_ari_score': validation_results[best_k]['ari_with_profiles'],
            'interpretation': self._interpret_profile_validation(
                validation_results[best_k]['ari_with_profiles']
            )
        }
    
    def _find_optimal_for_profiles(self, X_scaled: np.ndarray) -> dict:
        """Find optimal number of clusters for this specific dataset"""
        logger.info("Finding optimal clusters for profile data...")
        
        k_range = range(2, 8)
        inertias = []
        silhouette_scores = []
        
        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(X_scaled)
            
            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(X_scaled, cluster_labels))
        
        # Find elbow
        elbow_k = self._find_elbow_point(inertias) + 2  # +2 because range starts at 2
        
        # Best silhouette
        best_silhouette_k = k_range[np.argmax(silhouette_scores)]
        
        return {
            'k_range': list(k_range),
            'inertias': inertias,
            'silhouette_scores': silhouette_scores,
            'elbow_k': elbow_k,
            'best_silhouette_k': best_silhouette_k,
            'max_silhouette_score': max(silhouette_scores),
            'recommendation': self._recommend_optimal_k_for_profiles(
                elbow_k, best_silhouette_k, silhouette_scores
            )
        }
    
    def _analyze_profile_characteristics(self, user_data: pd.DataFrame) -> dict:
        """Analyze characteristics of each profile"""
        logger.info("Analyzing profile characteristics...")
        
        characteristics = {}
        
        for profile in user_data['main_profile'].unique():
            profile_data = user_data[user_data['main_profile'] == profile]
            
            characteristics[profile] = {
                'count': len(profile_data),
                'percentage': (len(profile_data) / len(user_data)) * 100,
                'avg_consistency': profile_data['consistency_score'].mean(),
                'avg_achievement': profile_data['achievement_rate'].mean(),
                'avg_balance': profile_data['gamification_balance'].mean(),
                'avg_cycles': profile_data['total_cycles'].mean(),
                'avg_activity_days': profile_data['activity_days'].mean(),
                'std_consistency': profile_data['consistency_score'].std(),
                'std_achievement': profile_data['achievement_rate'].std(),
                'std_balance': profile_data['gamification_balance'].std()
            }
        
        return characteristics
    
    def _enhance_profiles_with_clustering(self, X_scaled: np.ndarray, 
                                        user_data: pd.DataFrame, 
                                        k3_analysis: dict) -> dict:
        """Enhance profiles using clustering insights"""
        logger.info("Enhancing profiles with clustering insights...")
        
        cluster_labels = k3_analysis['cluster_labels']
        
        # Map clusters to profiles
        cluster_profile_mapping = {}
        for cluster_id in np.unique(cluster_labels):
            cluster_mask = cluster_labels == cluster_id
            cluster_users = user_data[cluster_mask]
            
            # Find dominant profile in this cluster
            profile_counts = cluster_users['main_profile'].value_counts()
            dominant_profile = profile_counts.index[0]
            
            cluster_profile_mapping[cluster_id] = {
                'dominant_profile': dominant_profile,
                'profile_distribution': profile_counts.to_dict(),
                'purity': profile_counts.iloc[0] / len(cluster_users),
                'avg_metrics': {
                    'consistency': cluster_users['consistency_score'].mean(),
                    'achievement': cluster_users['achievement_rate'].mean(),
                    'balance': cluster_users['gamification_balance'].mean(),
                    'cycles': cluster_users['total_cycles'].mean()
                }
            }
        
        return {
            'cluster_profile_mapping': cluster_profile_mapping,
            'enhanced_user_data': self._create_enhanced_user_data(
                user_data, cluster_labels, cluster_profile_mapping
            )
        }
    
    def _analyze_cluster_composition(self, user_data: pd.DataFrame, 
                                   cluster_labels: np.ndarray) -> dict:
        """Analyze composition of each cluster"""
        composition = {}
        
        for cluster_id in np.unique(cluster_labels):
            cluster_mask = cluster_labels == cluster_id
            cluster_users = user_data[cluster_mask]
            
            profile_counts = cluster_users['main_profile'].value_counts()
            
            composition[f'cluster_{cluster_id}'] = {
                'size': len(cluster_users),
                'profile_distribution': profile_counts.to_dict(),
                'dominant_profile': profile_counts.index[0] if len(profile_counts) > 0 else None,
                'purity': profile_counts.iloc[0] / len(cluster_users) if len(profile_counts) > 0 else 0
            }
        
        return composition
    
    def _find_elbow_point(self, inertias: list) -> int:
        """Find elbow point in inertia curve"""
        diffs = np.diff(inertias)
        diffs2 = np.diff(diffs)
        elbow_idx = np.argmax(diffs2)
        return elbow_idx
    
    def _recommend_optimal_k_for_profiles(self, elbow_k: int, silhouette_k: int, 
                                        silhouette_scores: list) -> dict:
        """Recommend optimal K considering profile context"""
        
        # Special consideration for K=3 (matches profiles)
        k3_silhouette = silhouette_scores[1]  # K=3 is index 1 (range starts at 2)
        
        recommendations = {
            'statistical_optimal': silhouette_k,
            'elbow_point': elbow_k,
            'profile_aligned': 3,
            'k3_silhouette': k3_silhouette
        }
        
        # Decision logic
        if silhouette_k == 3:
            recommendation = 3
            reason = "K=3 is both statistically optimal and profile-aligned"
            confidence = "High"
        elif abs(k3_silhouette - max(silhouette_scores)) < 0.05:
            recommendation = 3
            reason = "K=3 performance close to optimal, better interpretability"
            confidence = "Medium-High"
        else:
            recommendation = silhouette_k
            reason = f"K={silhouette_k} significantly better than K=3"
            confidence = "Medium"
        
        recommendations.update({
            'final_recommendation': recommendation,
            'reason': reason,
            'confidence': confidence
        })
        
        return recommendations
    
    def _interpret_profile_validation(self, ari_score: float) -> str:
        """Interpret ARI score for profile validation"""
        if ari_score > 0.7:
            return "Excellent: Clustering strongly validates existing profiles"
        elif ari_score > 0.5:
            return "Good: Clustering moderately validates existing profiles"
        elif ari_score > 0.3:
            return "Fair: Some alignment between clustering and existing profiles"
        else:
            return "Poor: Clustering suggests different profile structure"
    
    def _create_enhanced_user_data(self, user_data: pd.DataFrame, 
                                 cluster_labels: np.ndarray,
                                 cluster_mapping: dict) -> pd.DataFrame:
        """Create enhanced user data with clustering insights"""
        enhanced_data = user_data.copy()
        enhanced_data['cluster_id'] = cluster_labels
        
        # Add cluster-based insights
        enhanced_data['cluster_dominant_profile'] = enhanced_data['cluster_id'].map(
            lambda x: cluster_mapping[x]['dominant_profile']
        )
        enhanced_data['cluster_purity'] = enhanced_data['cluster_id'].map(
            lambda x: cluster_mapping[x]['purity']
        )
        
        # Profile-cluster alignment
        enhanced_data['profile_cluster_aligned'] = (
            enhanced_data['main_profile'] == enhanced_data['cluster_dominant_profile']
        )
        
        return enhanced_data
