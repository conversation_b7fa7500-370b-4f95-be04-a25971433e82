import pandas as pd
import numpy as np

# Load the profile-based clustering results
df = pd.read_csv('results/reports/profile_based_clustering_results.csv')

print('='*60)
print('FINAL CLUSTERING DECISION: K=2 CLUSTERS')
print('='*60)

print('\n📊 CLUSTER COMPOSITION:')
cluster_counts = df['cluster_id'].value_counts().sort_index()
for cluster_id, count in cluster_counts.items():
    percentage = (count / len(df)) * 100
    print(f'Cluster {cluster_id}: {count} users ({percentage:.1f}%)')

print('\n👥 CLUSTER 0 (HIGH PERFORMERS) ANALYSIS:')
cluster_0 = df[df['cluster_id'] == 0]
print(f'Total Users: {len(cluster_0)} ({len(cluster_0)/len(df)*100:.1f}%)')
print(f'Profile Distribution:')
profile_dist_0 = cluster_0['main_profile'].value_counts()
for profile, count in profile_dist_0.items():
    print(f'  {profile}: {count} users ({count/len(cluster_0)*100:.1f}%)')

print(f'\nAverage Metrics:')
print(f'  Consistency Score: {cluster_0["consistency_score"].mean():.2f}')
print(f'  Achievement Rate: {cluster_0["achievement_rate"].mean():.2f} ({cluster_0["achievement_rate"].mean()*100:.0f}%)')
print(f'  Gamification Balance: {cluster_0["gamification_balance"].mean():.1f}')
print(f'  Total Cycles: {cluster_0["total_cycles"].mean():.1f}')
print(f'  Activity Days: {cluster_0["activity_days"].mean():.1f}')

print('\n👥 CLUSTER 1 (STRUGGLING USERS) ANALYSIS:')
cluster_1 = df[df['cluster_id'] == 1]
print(f'Total Users: {len(cluster_1)} ({len(cluster_1)/len(df)*100:.1f}%)')
print(f'Profile Distribution:')
profile_dist_1 = cluster_1['main_profile'].value_counts()
for profile, count in profile_dist_1.items():
    print(f'  {profile}: {count} users ({count/len(cluster_1)*100:.1f}%)')

print(f'\nAverage Metrics:')
print(f'  Consistency Score: {cluster_1["consistency_score"].mean():.2f}')
print(f'  Achievement Rate: {cluster_1["achievement_rate"].mean():.2f} ({cluster_1["achievement_rate"].mean()*100:.0f}%)')
print(f'  Gamification Balance: {cluster_1["gamification_balance"].mean():.1f}')
print(f'  Total Cycles: {cluster_1["total_cycles"].mean():.1f}')
print(f'  Activity Days: {cluster_1["activity_days"].mean():.1f}')

print('\n🎯 CLUSTER SEPARATION ANALYSIS:')
print(f'Consistency Score Difference: {cluster_0["consistency_score"].mean() - cluster_1["consistency_score"].mean():.2f}')
print(f'Achievement Rate Difference: {(cluster_0["achievement_rate"].mean() - cluster_1["achievement_rate"].mean())*100:.0f} percentage points')
print(f'Cycles Difference: {cluster_0["total_cycles"].mean() - cluster_1["total_cycles"].mean():.1f}')

print('\n✅ PROFILE-CLUSTER ALIGNMENT:')
alignment = df['profile_cluster_aligned'].value_counts()
total_aligned = alignment.get(True, 0)
total_misaligned = alignment.get(False, 0)
alignment_rate = total_aligned / len(df) * 100
print(f'Aligned: {total_aligned} users ({alignment_rate:.1f}%)')
print(f'Misaligned: {total_misaligned} users ({100-alignment_rate:.1f}%)')

print('\n🎯 PERSONALIZATION STRATEGY DISTRIBUTION:')
print(f'High Performers (Advanced Strategy): {len(cluster_0)} users ({len(cluster_0)/len(df)*100:.1f}%)')
print(f'Struggling Users (Basic Strategy): {len(cluster_1)} users ({len(cluster_1)/len(df)*100:.1f}%)')

print('\n📈 BUSINESS IMPACT PROJECTION:')
print(f'Users needing intensive intervention: {len(cluster_1)} ({len(cluster_1)/len(df)*100:.1f}%)')
print(f'Users in maintenance mode: {len(cluster_0)} ({len(cluster_0)/len(df)*100:.1f}%)')
print(f'Resource allocation ratio: {len(cluster_1)/len(cluster_0):.1f}:1 (Struggling:High Performers)')

# Save summary to file
summary_data = {
    'cluster_0_count': len(cluster_0),
    'cluster_1_count': len(cluster_1),
    'cluster_0_percentage': len(cluster_0)/len(df)*100,
    'cluster_1_percentage': len(cluster_1)/len(df)*100,
    'alignment_rate': alignment_rate,
    'resource_ratio': len(cluster_1)/len(cluster_0)
}

print(f'\n💾 SUMMARY SAVED TO: k2_cluster_summary.txt')
with open('results/reports/k2_cluster_summary.txt', 'w') as f:
    f.write('K=2 CLUSTERING SUMMARY\n')
    f.write('='*30 + '\n\n')
    f.write(f'Cluster 0 (High Performers): {len(cluster_0)} users ({len(cluster_0)/len(df)*100:.1f}%)\n')
    f.write(f'Cluster 1 (Struggling Users): {len(cluster_1)} users ({len(cluster_1)/len(df)*100:.1f}%)\n')
    f.write(f'Profile-Cluster Alignment: {alignment_rate:.1f}%\n')
    f.write(f'Resource Allocation Ratio: {len(cluster_1)/len(cluster_0):.1f}:1\n')
