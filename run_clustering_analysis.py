"""
Run Clustering Analysis on User Profiles
Extends the existing correlation-mediation-moderation analysis with clustering
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
import sys

# Add src to path
sys.path.append('src')

from clustering_analyzer import UserClusteringAnalyzer
from data_processor import DataProcessor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main clustering analysis pipeline"""
    
    logger.info("="*60)
    logger.info("STARTING CLUSTERING ANALYSIS PIPELINE")
    logger.info("="*60)
    
    try:
        # Setup paths
        data_path = Path("dataset/processed")
        results_path = Path("results")
        visualizations_path = results_path / "visualizations"
        reports_path = results_path / "reports"
        
        # Ensure directories exist
        visualizations_path.mkdir(parents=True, exist_ok=True)
        reports_path.mkdir(parents=True, exist_ok=True)
        
        # Step 1: Load processed data
        logger.info("Step 1: Loading processed data...")
        data_file = data_path / "weekly_merged_dataset_with_gamification.csv"
        
        if not data_file.exists():
            logger.error(f"Data file not found: {data_file}")
            logger.info("Please run main.py first to generate processed data")
            return
        
        data = pd.read_csv(data_file)
        logger.info(f"✅ Data loaded: {len(data)} observations")
        
        # Step 2: Initialize clustering analyzer
        logger.info("Step 2: Initializing clustering analyzer...")
        clustering_analyzer = UserClusteringAnalyzer(visualizations_path)
        
        # Step 3: Prepare user-level data
        logger.info("Step 3: Preparing user-level data for clustering...")
        user_data = clustering_analyzer.prepare_clustering_data(data)
        logger.info(f"✅ User data prepared: {len(user_data)} unique users")
        
        # Display existing profile distribution
        logger.info("\nExisting Profile Distribution:")
        profile_counts = user_data['existing_profile'].value_counts()
        for profile, count in profile_counts.items():
            percentage = (count / len(user_data)) * 100
            logger.info(f"  {profile}: {count} users ({percentage:.1f}%)")
        
        # Step 4: Perform comprehensive clustering analysis
        logger.info("\nStep 4: Performing comprehensive clustering analysis...")
        clustering_results = clustering_analyzer.perform_clustering_analysis(user_data)
        
        # Display key results
        validation = clustering_results['profile_validation']
        logger.info(f"\n📊 CLUSTERING VALIDATION RESULTS:")
        logger.info(f"  Adjusted Rand Index: {validation['adjusted_rand_index']:.3f}")
        logger.info(f"  Silhouette Score: {validation['silhouette_score']:.3f}")
        logger.info(f"  Interpretation: {validation['interpretation']}")
        
        optimal = clustering_results['optimal_clusters']
        logger.info(f"\n🎯 OPTIMAL CLUSTERS:")
        logger.info(f"  Recommended K: {optimal['recommended_k']['recommended_k']}")
        logger.info(f"  Confidence: {optimal['recommended_k']['confidence']}")
        logger.info(f"  Reasoning: {optimal['recommended_k']['reasoning']}")
        
        # Find best algorithm
        best_algorithm = max(
            clustering_results['clustering_results'].items(),
            key=lambda x: x[1]['silhouette_score']
        )
        logger.info(f"\n🏆 BEST ALGORITHM:")
        logger.info(f"  Algorithm: {best_algorithm[0]}")
        logger.info(f"  Silhouette Score: {best_algorithm[1]['silhouette_score']:.3f}")
        logger.info(f"  Agreement with Existing: {best_algorithm[1]['ari_with_existing']:.3f}")
        
        # Sub-profiles
        if clustering_results['sub_profiles']:
            logger.info(f"\n🔍 SUB-PROFILES DISCOVERED:")
            for profile, sub_data in clustering_results['sub_profiles'].items():
                logger.info(f"  {profile}: {sub_data['n_sub_clusters']} sub-clusters "
                          f"(Silhouette: {sub_data['silhouette_score']:.3f})")
        else:
            logger.info(f"\n🔍 SUB-PROFILES: None discovered")
        
        # Feature importance
        top_features = clustering_results['feature_importance']['top_features']
        logger.info(f"\n📈 TOP FEATURES FOR CLUSTERING:")
        for i, (feature, importance) in enumerate(top_features, 1):
            logger.info(f"  {i}. {feature}: {importance:.3f}")
        
        # Step 5: Create visualizations
        logger.info("\nStep 5: Creating clustering visualizations...")
        clustering_analyzer.create_clustering_visualizations(user_data, clustering_results)
        logger.info("✅ Clustering visualizations completed")
        
        # Step 6: Generate comprehensive report
        logger.info("Step 6: Generating clustering report...")
        clustering_report = clustering_analyzer.generate_clustering_report(user_data, clustering_results)
        
        # Save clustering report
        report_file = reports_path / "clustering_analysis_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(clustering_report)
        logger.info(f"✅ Clustering report saved: {report_file}")
        
        # Step 7: Save clustering results data
        logger.info("Step 7: Saving clustering results data...")
        
        # Save user data with cluster assignments
        user_data_with_clusters = user_data.copy()
        
        # Add best clustering results
        best_cluster_labels = best_algorithm[1]['cluster_labels']
        user_data_with_clusters['cluster_assignment'] = best_cluster_labels
        user_data_with_clusters['clustering_algorithm'] = best_algorithm[0]
        
        # Save to CSV
        clustering_data_file = reports_path / "user_clustering_results.csv"
        user_data_with_clusters.to_csv(clustering_data_file, index=False)
        logger.info(f"✅ Clustering data saved: {clustering_data_file}")
        
        # Step 8: Create enhanced user profiles
        logger.info("Step 8: Creating enhanced user profiles...")
        enhanced_profiles = create_enhanced_profiles(user_data_with_clusters, clustering_results)
        
        # Save enhanced profiles
        enhanced_profiles_file = reports_path / "enhanced_user_profiles.csv"
        enhanced_profiles.to_csv(enhanced_profiles_file, index=False)
        logger.info(f"✅ Enhanced profiles saved: {enhanced_profiles_file}")
        
        # Step 9: Generate implementation recommendations
        logger.info("Step 9: Generating implementation recommendations...")
        implementation_guide = generate_implementation_guide(clustering_results, user_data)
        
        implementation_file = reports_path / "clustering_implementation_guide.md"
        with open(implementation_file, 'w', encoding='utf-8') as f:
            f.write(implementation_guide)
        logger.info(f"✅ Implementation guide saved: {implementation_file}")
        
        # Final summary
        logger.info("\n" + "="*60)
        logger.info("🎉 CLUSTERING ANALYSIS COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        
        logger.info(f"\n📁 FILES GENERATED:")
        logger.info(f"   📊 Visualizations: 5 new clustering charts")
        logger.info(f"   📋 Reports: clustering_analysis_report.md")
        logger.info(f"   📈 Data: user_clustering_results.csv")
        logger.info(f"   👥 Profiles: enhanced_user_profiles.csv")
        logger.info(f"   🔧 Guide: clustering_implementation_guide.md")
        
        logger.info(f"\n🎯 KEY INSIGHTS:")
        logger.info(f"   • Existing profiles are {'VALIDATED' if validation['adjusted_rand_index'] > 0.5 else 'CHALLENGED'} by clustering")
        logger.info(f"   • Optimal number of clusters: {optimal['recommended_k']['recommended_k']}")
        logger.info(f"   • Best algorithm: {best_algorithm[0]}")
        logger.info(f"   • Sub-profiles discovered: {len(clustering_results['sub_profiles'])}")
        logger.info(f"   • Most important feature: {top_features[0][0]}")
        
        logger.info(f"\n✅ Ready for advanced personalization implementation!")
        
    except Exception as e:
        logger.error(f"❌ Error in clustering analysis: {str(e)}")
        raise

def create_enhanced_profiles(user_data: pd.DataFrame, clustering_results: dict) -> pd.DataFrame:
    """Create enhanced user profiles combining existing and clustering insights"""
    
    enhanced = user_data.copy()
    
    # Add clustering confidence score
    validation_score = clustering_results['profile_validation']['adjusted_rand_index']
    enhanced['profile_confidence'] = 'High' if validation_score > 0.7 else 'Medium' if validation_score > 0.4 else 'Low'
    
    # Add sub-profile information
    enhanced['sub_profile'] = 'None'
    for profile_name, sub_data in clustering_results['sub_profiles'].items():
        profile_mask = enhanced['existing_profile'] == profile_name
        if profile_mask.any():
            sub_labels = sub_data['labels']
            enhanced.loc[profile_mask, 'sub_profile'] = [f"{profile_name}_Sub_{label}" for label in sub_labels]
    
    # Add feature importance ranking
    feature_importance = clustering_results['feature_importance']['feature_importance']
    
    # Calculate user's strength in top features
    top_feature = max(feature_importance.items(), key=lambda x: x[1])[0]
    enhanced['top_feature_strength'] = enhanced[top_feature]
    enhanced['top_feature_name'] = top_feature
    
    # Add personalization recommendations
    enhanced['personalization_priority'] = enhanced.apply(
        lambda row: get_personalization_priority(row), axis=1
    )
    
    return enhanced

def get_personalization_priority(user_row) -> str:
    """Determine personalization priority for a user"""
    
    profile = user_row['existing_profile']
    confidence = user_row['profile_confidence']
    
    if profile == "Struggling Beginner":
        return "High - Basic intervention needed"
    elif profile == "Imbalanced Moderate":
        return "High - Balance correction needed"
    elif profile == "Balanced Achiever" and confidence == "Low":
        return "Medium - Profile refinement needed"
    elif profile == "Optimal Performer":
        return "Low - Maintenance mode"
    else:
        return "Medium - Standard optimization"

def generate_implementation_guide(clustering_results: dict, user_data: pd.DataFrame) -> str:
    """Generate implementation guide for clustering insights"""
    
    validation_score = clustering_results['profile_validation']['adjusted_rand_index']
    optimal_k = clustering_results['optimal_clusters']['recommended_k']['recommended_k']
    best_algorithm = max(clustering_results['clustering_results'].items(), 
                        key=lambda x: x[1]['silhouette_score'])
    
    guide = f"""# CLUSTERING IMPLEMENTATION GUIDE
## Panduan Implementasi Hasil Analisis Clustering

---

## 🎯 EXECUTIVE SUMMARY

Analisis clustering pada {len(user_data)} pengguna menghasilkan insights untuk personalisasi advanced:

- **Profile Validation**: {'STRONG' if validation_score > 0.7 else 'MODERATE' if validation_score > 0.4 else 'WEAK'} (ARI: {validation_score:.3f})
- **Optimal Clusters**: {optimal_k}
- **Best Algorithm**: {best_algorithm[0]}
- **Sub-profiles**: {len(clustering_results['sub_profiles'])} discovered

---

## 🔧 IMPLEMENTATION ROADMAP

### Phase 1: Profile System Enhancement (Weeks 1-2)
"""
    
    if validation_score > 0.5:
        guide += """
✅ **Action**: Maintain existing 4-profile system
- Existing profiles are well-validated by clustering
- Focus on optimization rather than restructuring
- Implement sub-profile enhancements where discovered
"""
    else:
        guide += f"""
⚠️ **Action**: Consider profile system revision
- Current profiles show weak clustering validation (ARI: {validation_score:.3f})
- Recommend transition to {optimal_k}-cluster system
- Gradual migration with A/B testing
"""
    
    guide += f"""

### Phase 2: Algorithm Implementation (Weeks 3-4)
✅ **Recommended Algorithm**: {best_algorithm[0]}
- Silhouette Score: {best_algorithm[1]['silhouette_score']:.3f}
- Implementation complexity: {'Low' if 'KMeans' in best_algorithm[0] else 'Medium' if 'Hierarchical' in best_algorithm[0] else 'High'}
- Real-time clustering: {'Yes' if 'KMeans' in best_algorithm[0] else 'Batch only'}

### Phase 3: Sub-Profile Integration (Weeks 5-6)
"""
    
    if clustering_results['sub_profiles']:
        guide += f"""
✅ **Sub-profiles discovered**: {len(clustering_results['sub_profiles'])}
"""
        for profile, sub_data in clustering_results['sub_profiles'].items():
            guide += f"- {profile}: {sub_data['n_sub_clusters']} sub-clusters\n"
        
        guide += """
**Implementation**:
- Add sub-profile field to user database
- Create sub-profile specific recommendations
- A/B test sub-profile vs main profile personalization
"""
    else:
        guide += """
ℹ️ **No sub-profiles needed**
- Current profiles are sufficiently granular
- Focus on main profile optimization
- Monitor for future sub-profile opportunities
"""
    
    # Add feature importance recommendations
    top_features = clustering_results['feature_importance']['top_features']
    guide += f"""

### Phase 4: Feature-Based Personalization (Weeks 7-8)
🎯 **Priority Features** (in order of importance):
"""
    for i, (feature, importance) in enumerate(top_features, 1):
        guide += f"{i}. **{feature}**: {importance:.3f}\n"
    
    guide += f"""

**Implementation Strategy**:
- Use {top_features[0][0]} as primary personalization driver
- Create feature-specific user segments
- Implement dynamic feature weighting

---

## 📊 TECHNICAL SPECIFICATIONS

### Database Schema Updates
```sql
-- Add clustering fields to user table
ALTER TABLE users ADD COLUMN cluster_id INTEGER;
ALTER TABLE users ADD COLUMN sub_profile VARCHAR(50);
ALTER TABLE users ADD COLUMN profile_confidence VARCHAR(20);
ALTER TABLE users ADD COLUMN last_clustering_update TIMESTAMP;
```

### API Endpoints
```python
# Get user cluster assignment
GET /api/users/{{user_id}}/cluster

# Update user clustering
POST /api/users/{{user_id}}/cluster
{{
    "cluster_id": {optimal_k},
    "algorithm": "{best_algorithm[0]}",
    "confidence": "High"
}}

# Get cluster-based recommendations
GET /api/recommendations/cluster/{{cluster_id}}
```

### Monitoring Metrics
- Cluster stability over time
- User migration between clusters
- Personalization effectiveness by cluster
- Sub-profile engagement rates

---

## 🎯 SUCCESS METRICS

### Primary KPIs
- User engagement increase: Target +15%
- Personalization accuracy: Target +25%
- User satisfaction: Target +20%

### Secondary KPIs
- Cluster stability: >80% users stay in same cluster monthly
- Sub-profile utilization: >60% of eligible users
- Algorithm performance: Silhouette score >0.4

---

## ⚠️ RISKS AND MITIGATION

### Risk 1: Profile Migration Confusion
- **Mitigation**: Gradual transition with user communication
- **Timeline**: 2-week overlap period

### Risk 2: Over-segmentation
- **Mitigation**: Start with main clusters, add sub-profiles gradually
- **Monitoring**: Track engagement by segment size

### Risk 3: Algorithm Performance Degradation
- **Mitigation**: Monthly re-clustering and validation
- **Fallback**: Revert to existing profiles if needed

---

## 📋 IMPLEMENTATION CHECKLIST

### Week 1-2: Foundation
- [ ] Database schema updates
- [ ] User data migration
- [ ] Basic clustering API implementation

### Week 3-4: Algorithm Integration
- [ ] {best_algorithm[0]} implementation
- [ ] Batch clustering pipeline
- [ ] Real-time assignment logic

### Week 5-6: Sub-Profile System
- [ ] Sub-profile detection
- [ ] Enhanced recommendation engine
- [ ] A/B testing framework

### Week 7-8: Feature Optimization
- [ ] Feature-based personalization
- [ ] Dynamic weighting system
- [ ] Performance monitoring

### Week 9-10: Validation & Launch
- [ ] End-to-end testing
- [ ] Performance validation
- [ ] Gradual rollout to users

---

## 🚀 NEXT STEPS

1. **Immediate (This Week)**:
   - Review clustering results with stakeholders
   - Approve implementation roadmap
   - Assign development resources

2. **Short-term (Next Month)**:
   - Begin Phase 1 implementation
   - Set up monitoring infrastructure
   - Prepare A/B testing framework

3. **Long-term (Next Quarter)**:
   - Full clustering system deployment
   - Advanced personalization features
   - Continuous optimization pipeline

---

*Implementation guide generated from clustering analysis of {len(user_data)} users with validated insights and actionable recommendations.*
"""
    
    return guide

if __name__ == "__main__":
    main()
