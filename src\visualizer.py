"""
Visualization Module
Clean code implementation for creating publication-ready visualizations
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class Visualizer:
    """
    Clean visualization class following SOLID principles
    """
    
    def __init__(self, output_path: str = "results/visualizations"):
        """
        Initialize visualizer
        
        Args:
            output_path: Path to save visualizations
        """
        self.output_path = Path(output_path)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Configure matplotlib for high quality output
        plt.rcParams['figure.dpi'] = 300
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['font.size'] = 10
        plt.rcParams['axes.titlesize'] = 12
        plt.rcParams['axes.labelsize'] = 10
        plt.rcParams['xtick.labelsize'] = 9
        plt.rcParams['ytick.labelsize'] = 9
        plt.rcParams['legend.fontsize'] = 9
    
    def create_correlation_scatter(self, data: pd.DataFrame,
                                 x_var: str, y_var: str,
                                 title: str, filename: str,
                                 correlation: Optional[float] = None) -> None:
        """
        Create scatter plot for correlation visualization
        
        Args:
            data: Input dataset
            x_var: X-axis variable
            y_var: Y-axis variable  
            title: Plot title
            filename: Output filename
            correlation: Correlation coefficient to display
        """
        logger.info(f"Creating scatter plot: {filename}")
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create scatter plot
        scatter = ax.scatter(data[x_var], data[y_var], 
                           alpha=0.6, s=50, color='steelblue')
        
        # Add trend line
        z = np.polyfit(data[x_var], data[y_var], 1)
        p = np.poly1d(z)
        ax.plot(data[x_var], p(data[x_var]), "r--", alpha=0.8, linewidth=2)
        
        # Customize plot
        ax.set_xlabel(self._format_variable_name(x_var))
        ax.set_ylabel(self._format_variable_name(y_var))
        ax.set_title(title, fontweight='bold', pad=20)
        
        # Add correlation info if provided
        if correlation is not None:
            ax.text(0.05, 0.95, f'r = {correlation:.3f}', 
                   transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # Add grid
        ax.grid(True, alpha=0.3)
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_comparison_plot(self, data: pd.DataFrame,
                             variables: List[str], target: str,
                             title: str, filename: str) -> None:
        """
        Create comparison plot for multiple variables
        
        Args:
            data: Input dataset
            variables: List of variables to compare
            target: Target variable
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating comparison plot: {filename}")
        
        fig, axes = plt.subplots(1, len(variables), figsize=(15, 6))
        
        if len(variables) == 1:
            axes = [axes]
        
        for i, var in enumerate(variables):
            if var in data.columns:
                # Calculate correlation
                correlation = data[var].corr(data[target])
                
                # Create scatter plot
                axes[i].scatter(data[var], data[target], 
                              alpha=0.6, s=30, color=f'C{i}')
                
                # Add trend line
                z = np.polyfit(data[var], data[target], 1)
                p = np.poly1d(z)
                axes[i].plot(data[var], p(data[var]), "r--", alpha=0.8)
                
                # Customize subplot
                axes[i].set_xlabel(self._format_variable_name(var))
                axes[i].set_ylabel(self._format_variable_name(target))
                axes[i].set_title(f'{self._format_variable_name(var)}\nr = {correlation:.3f}')
                axes[i].grid(True, alpha=0.3)
        
        # Main title
        fig.suptitle(title, fontsize=14, fontweight='bold')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_achievement_comparison(self, data: pd.DataFrame,
                                    achievement_var: str, target_var: str,
                                    title: str, filename: str) -> None:
        """
        Create achievement level comparison plot
        
        Args:
            data: Input dataset
            achievement_var: Achievement variable
            target_var: Target variable
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating achievement comparison: {filename}")
        
        # Create achievement categories
        data_copy = data.copy()
        data_copy['achievement_category'] = pd.cut(
            data_copy[achievement_var], 
            bins=[0, 0.33, 0.66, 1.0], 
            labels=['Low', 'Medium', 'High']
        )
        
        # Calculate means by category
        means = data_copy.groupby('achievement_category')[target_var].mean()
        counts = data_copy.groupby('achievement_category').size()
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create bar plot
        bars = ax.bar(means.index, means.values, 
                     color=['#ff7f7f', '#ffbf7f', '#7fbf7f'], alpha=0.8)
        
        # Add value labels on bars
        for i, (bar, mean, count) in enumerate(zip(bars, means.values, counts.values)):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                   f'{mean:.1f}\n(n={count})', ha='center', va='bottom', fontweight='bold')
        
        # Customize plot
        ax.set_xlabel('Achievement Level')
        ax.set_ylabel(self._format_variable_name(target_var))
        ax.set_title(title, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3, axis='y')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_distribution_plot(self, data: pd.DataFrame,
                               variable: str, title: str, filename: str) -> None:
        """
        Create distribution plot with statistics
        
        Args:
            data: Input dataset
            variable: Variable to plot
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating distribution plot: {filename}")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Histogram
        ax1.hist(data[variable], bins=20, alpha=0.7, color='steelblue', edgecolor='black')
        ax1.set_xlabel(self._format_variable_name(variable))
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Box plot
        ax2.boxplot(data[variable], vert=True)
        ax2.set_ylabel(self._format_variable_name(variable))
        ax2.set_title('Box Plot')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics
        stats_text = f"""
        Mean: {data[variable].mean():.2f}
        Median: {data[variable].median():.2f}
        Std: {data[variable].std():.2f}
        Min: {data[variable].min():.2f}
        Max: {data[variable].max():.2f}
        """
        
        fig.text(0.02, 0.98, stats_text, transform=fig.transFigure, 
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        # Main title
        fig.suptitle(title, fontsize=14, fontweight='bold')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def _format_variable_name(self, var_name: str) -> str:
        """Format variable name for display"""
        
        name_mapping = {
            'total_cycles': 'Total Cycles (per week)',
            'consistency_score': 'Consistency Score',
            'activity_days': 'Activity Days (per week)',
            'avg_intensity': 'Average Intensity (km/h)',
            'total_gamification_points': 'Total Gamification Points',
            'achievement_rate': 'Achievement Rate (%)',
            'gamification_balance': 'Gamification Balance',
            'activity_points': 'Activity Points',
            'productivity_points': 'Productivity Points'
        }
        
        return name_mapping.get(var_name, var_name.replace('_', ' ').title())
    
    def create_all_visualizations(self, data: pd.DataFrame,
                                correlation_results: pd.DataFrame,
                                moderation_results: list = None) -> None:
        """
        Create all standard visualizations

        Args:
            data: Input dataset
            correlation_results: Correlation analysis results
            moderation_results: Moderation analysis results (optional)
        """
        logger.info("Creating all visualizations...")
        
        # Get significant correlations
        significant = correlation_results[correlation_results['significant']].copy()
        
        if significant.empty:
            logger.warning("No significant correlations found for visualization")
            return
        
        # 1. Main finding: Strongest correlation
        strongest = significant.iloc[0]
        self.create_correlation_scatter(
            data, strongest['predictor_variable'], strongest['target_variable'],
            f"Main Finding: {self._format_variable_name(strongest['predictor_variable'])} vs Productivity",
            "01_main_finding.png",
            strongest['correlation']
        )
        
        # 2. Gamification effect
        gamification_vars = ['total_gamification_points', 'activity_points', 'productivity_points']
        available_gamification = [var for var in gamification_vars if var in data.columns]
        
        if available_gamification:
            self.create_comparison_plot(
                data, available_gamification[:2], 'total_cycles',
                "Gamification Effects on Productivity",
                "02_gamification_effects.png"
            )
        
        # 3. Achievement comparison
        if 'achievement_rate' in data.columns:
            self.create_achievement_comparison(
                data, 'achievement_rate', 'total_cycles',
                "Productivity by Achievement Level",
                "03_achievement_comparison.png"
            )
        
        # 4. Productivity distribution
        self.create_distribution_plot(
            data, 'total_cycles',
            "Productivity Distribution Analysis",
            "04_productivity_distribution.png"
        )
        
        logger.info("All visualizations created successfully")

    def create_moderation_visualizations(self, data: pd.DataFrame,
                                       moderation_results: list) -> None:
        """
        Create visualizations for moderation analysis

        Args:
            data: Input dataset
            moderation_results: List of moderation analysis results
        """
        logger.info("Creating moderation visualizations...")

        # Filter significant moderation effects
        significant_moderations = [
            mod for mod in moderation_results
            if mod.get('interaction_p_value', 1) < 0.05
        ]

        if not significant_moderations:
            logger.warning("No significant moderation effects found")
            return

        # 1. Gamification Balance Moderation (most important)
        balance_moderations = [
            mod for mod in significant_moderations
            if mod['moderator_variable'] == 'gamification_balance'
        ]

        if balance_moderations:
            # Focus on consistency score moderation
            consistency_mod = next(
                (mod for mod in balance_moderations if mod['x_variable'] == 'consistency_score'),
                balance_moderations[0]
            )
            self.create_interaction_plot(
                data, consistency_mod,
                "Gamification Balance Moderates Consistency-Productivity Relationship",
                "05_gamification_balance_moderation.png"
            )

        # 2. Achievement Rate Moderation
        achievement_moderations = [
            mod for mod in significant_moderations
            if mod['moderator_variable'] == 'achievement_rate'
        ]

        if achievement_moderations:
            self.create_interaction_plot(
                data, achievement_moderations[0],
                "Achievement Rate Moderates Gamification-Productivity Relationship",
                "06_achievement_rate_moderation.png"
            )

        # 3. Activity Days Moderation
        activity_moderations = [
            mod for mod in significant_moderations
            if mod['moderator_variable'] == 'activity_days'
        ]

        if activity_moderations:
            self.create_interaction_plot(
                data, activity_moderations[0],
                "Activity Frequency Moderates Consistency-Productivity Relationship",
                "07_activity_frequency_moderation.png"
            )

        # 4. Moderation Summary
        self.create_moderation_summary(
            significant_moderations,
            "Moderation Effects Summary",
            "08_moderation_summary.png"
        )

        logger.info("Moderation visualizations created successfully")

    def create_interaction_plot(self, data: pd.DataFrame, moderation_result: dict,
                              title: str, filename: str) -> None:
        """
        Create interaction plot for moderation analysis

        Args:
            data: Input dataset
            moderation_result: Moderation analysis result
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating interaction plot: {filename}")

        x_var = moderation_result['x_variable']
        mod_var = moderation_result['moderator_variable']
        y_var = moderation_result['y_variable']

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Split data by moderator (high/low)
        moderator_median = moderation_result['moderator_median']
        high_mod = data[data[mod_var] >= moderator_median].copy()
        low_mod = data[data[mod_var] < moderator_median].copy()

        # Plot 1: Scatter plot with different colors for high/low moderator
        ax1.scatter(low_mod[x_var], low_mod[y_var],
                   alpha=0.6, s=50, color='blue', label=f'Low {self._format_variable_name(mod_var)}')
        ax1.scatter(high_mod[x_var], high_mod[y_var],
                   alpha=0.6, s=50, color='red', label=f'High {self._format_variable_name(mod_var)}')

        # Add trend lines
        if len(low_mod) > 1:
            z_low = np.polyfit(low_mod[x_var], low_mod[y_var], 1)
            p_low = np.poly1d(z_low)
            x_range_low = np.linspace(low_mod[x_var].min(), low_mod[x_var].max(), 100)
            ax1.plot(x_range_low, p_low(x_range_low), "b-", alpha=0.8, linewidth=2)

        if len(high_mod) > 1:
            z_high = np.polyfit(high_mod[x_var], high_mod[y_var], 1)
            p_high = np.poly1d(z_high)
            x_range_high = np.linspace(high_mod[x_var].min(), high_mod[x_var].max(), 100)
            ax1.plot(x_range_high, p_high(x_range_high), "r-", alpha=0.8, linewidth=2)

        ax1.set_xlabel(self._format_variable_name(x_var))
        ax1.set_ylabel(self._format_variable_name(y_var))
        ax1.set_title('Interaction Effect')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Add correlation info
        r_low = moderation_result['simple_slope_low_moderator']
        r_high = moderation_result['simple_slope_high_moderator']
        ax1.text(0.05, 0.95, f'Low {mod_var}: r = {r_low:.3f}',
                transform=ax1.transAxes, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        ax1.text(0.05, 0.85, f'High {mod_var}: r = {r_high:.3f}',
                transform=ax1.transAxes, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.8))

        # Plot 2: Simple slopes visualization
        moderator_levels = ['Low', 'High']
        slopes = [r_low, r_high]
        colors = ['blue', 'red']

        bars = ax2.bar(moderator_levels, slopes, color=colors, alpha=0.7)
        ax2.set_ylabel('Correlation Coefficient')
        ax2.set_title('Simple Slopes Analysis')
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar, slope in zip(bars, slopes):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01 if height >= 0 else height - 0.03,
                    f'{slope:.3f}', ha='center', va='bottom' if height >= 0 else 'top')

        # Add interaction effect info
        interaction_r = moderation_result['interaction_effect']
        interaction_p = moderation_result['interaction_p_value']
        fig.suptitle(f'{title}\nInteraction Effect: r = {interaction_r:.3f}, p = {interaction_p:.3f}',
                    fontsize=14, fontweight='bold')

        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, dpi=VisualizationConstants.DPI, bbox_inches='tight')
        plt.close()

        logger.info(f"Saved: {filename}")

    def create_moderation_summary(self, moderation_results: list,
                                title: str, filename: str) -> None:
        """
        Create summary visualization of moderation effects

        Args:
            moderation_results: List of moderation results
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating moderation summary: {filename}")

        # Prepare data for visualization
        moderator_names = []
        interaction_effects = []
        p_values = []

        for mod in moderation_results:
            moderator_name = self._format_variable_name(mod['moderator_variable'])
            moderator_names.append(moderator_name)
            interaction_effects.append(abs(mod['interaction_effect']))  # Use absolute value for magnitude
            p_values.append(mod['interaction_p_value'])

        # Create figure
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # Plot 1: Interaction effect magnitudes
        colors = ['red' if p < 0.001 else 'orange' if p < 0.01 else 'yellow' for p in p_values]
        bars1 = ax1.barh(moderator_names, interaction_effects, color=colors, alpha=0.7)

        ax1.set_xlabel('Interaction Effect Magnitude |r|')
        ax1.set_title('Moderation Effect Strengths')
        ax1.grid(True, alpha=0.3, axis='x')

        # Add value labels
        for i, (bar, effect) in enumerate(zip(bars1, interaction_effects)):
            ax1.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                    f'{effect:.3f}', ha='left', va='center')

        # Add legend for significance levels
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='red', alpha=0.7, label='p < 0.001'),
            Patch(facecolor='orange', alpha=0.7, label='p < 0.01'),
            Patch(facecolor='yellow', alpha=0.7, label='p < 0.05')
        ]
        ax1.legend(handles=legend_elements, loc='lower right')

        # Plot 2: P-values (significance)
        bars2 = ax2.barh(moderator_names, [-np.log10(p) for p in p_values],
                        color=colors, alpha=0.7)

        ax2.set_xlabel('-log10(p-value)')
        ax2.set_title('Statistical Significance')
        ax2.grid(True, alpha=0.3, axis='x')
        ax2.axvline(x=-np.log10(0.05), color='red', linestyle='--', alpha=0.7, label='p = 0.05')
        ax2.axvline(x=-np.log10(0.01), color='orange', linestyle='--', alpha=0.7, label='p = 0.01')
        ax2.axvline(x=-np.log10(0.001), color='darkred', linestyle='--', alpha=0.7, label='p = 0.001')

        # Add value labels
        for i, (bar, p_val) in enumerate(zip(bars2, p_values)):
            ax2.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                    f'p={p_val:.3f}', ha='left', va='center')

        ax2.legend(loc='lower right')

        # Main title
        fig.suptitle(title, fontsize=16, fontweight='bold')

        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, dpi=VisualizationConstants.DPI, bbox_inches='tight')
        plt.close()

        logger.info(f"Saved: {filename}")


def main():
    """Main function for testing visualizer"""
    # Load processed data
    data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
    results_path = Path("results/reports/significant_correlations.csv")
    
    if not data_path.exists():
        print("Processed data not found. Run data_processor.py first.")
        return
    
    if not results_path.exists():
        print("Correlation results not found. Run correlation_analyzer.py first.")
        return
    
    data = pd.read_csv(data_path)
    correlation_results = pd.read_csv(results_path)
    
    # Create visualizations
    visualizer = Visualizer()
    visualizer.create_all_visualizations(data, correlation_results)
    
    print("Visualizations created successfully!")


if __name__ == "__main__":
    main()
